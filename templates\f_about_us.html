<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Button Styles */
        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem;
        }

        .modal-buttons .btn {
            width: auto;
            min-width: 160px;
            max-width: 80%;
            margin: 0 auto;
        }

        .social-login-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .social-login-btn img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-auth-content {
            display: flex;
            align-items: center;
            gap: 0;
            padding: 0.5rem 1rem;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px 0 0 8px;
            border-right: none;
            background: white;
            cursor: pointer;
            font-size: 1rem;
            color: var(--primary-blue);
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 1rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            width: max-content;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
            margin-right: 1rem;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-type-button:hover + .search-bar {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar input:focus {
            outline: none;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            padding: 2rem;
            border-right: 1px solid #e0e0e0;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 1rem;
        }

        .sidebar-nav a {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.95rem;
        }

        .sidebar-nav a:hover {
            color: var(--primary-pink);
        }

        .sidebar-nav .active {
            color: var(--primary-pink);
            font-weight: bold;
            border-left: 3px solid var(--primary-pink);
            padding-left: 1rem;
        }

        /* Main Content Styles */
        .main-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1rem;
        }

        .main-content h1 {
            font-size: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .main-content p {
            margin-bottom: 1.5rem;
            text-align: justify;
        }

        .last-updated {
            color: var(--text-gray);
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }

        .section {
            margin-bottom: 2rem;
        }

        .section h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .section-content {
            color: var(--text-gray);
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 2rem;
            left: -100%;
            height: 100vh;
            width: 100%;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 60px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            padding: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
            font-size: 1.2rem;
            text-align: center;
            margin: 10px 0;
            font-weight: 500;
        }

        .nav-item:hover {
            color: var(--primary-pink);
        }

        .side-nav.active + .hamburger span {
            background-color: var(--primary-blue);
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }

        .social-icons .bi {
            font-size: 1.5rem;
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        /* Hamburger Menu Styles */
        .hamburger {
            position: relative; /* Change from fixed to relative */
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        /* Mobile Search Icon */
        .mobile-search-icon {
            display: none;
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            transition: color 0.3s ease;
        }

        .mobile-search-icon:hover {
            color: var(--primary-pink);
        }

        /* Expandable Search Overlay */
        .search-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            display: none;
            align-items: flex-start;
            justify-content: center;
            padding-top: 120px;
        }

        .search-overlay.active {
            display: flex;
        }

        .expanded-search-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .search-overlay.active .expanded-search-container {
            transform: scale(1);
            opacity: 1;
        }

        .expanded-search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .expanded-search-header h3 {
            color: var(--primary-blue);
            font-size: 1.5rem;
            margin: 0;
        }

        .close-search-btn {
            background: none;
            border: none;
            font-size: 2rem;
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .close-search-btn:hover {
            color: var(--primary-pink);
        }

        .expanded-search-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .expanded-search-type {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .search-type-chip {
            padding: 0.5rem 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 20px;
            background: white;
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .search-type-chip.active,
        .search-type-chip:hover {
            background: var(--primary-blue);
            color: white;
        }

        .expanded-search-input {
            display: flex;
            align-items: center;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            background: white;
            height: 50px;
        }

        .expanded-search-input input {
            flex: 1;
            border: none;
            outline: none;
            padding: 0 1rem;
            font-size: 1.1rem;
            height: 100%;
        }

        .expanded-search-input .search-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 0 1.5rem;
            height: 100%;
            border-radius: 0 6px 6px 0;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }

        .expanded-search-input .search-btn:hover {
            background: var(--primary-pink);
        }

        /* Mobile auth buttons in hamburger menu */
        .mobile-auth-buttons {
            display: flex;
            flex-direction: row;
            gap: 1rem;
            margin-top: 0.5rem;
            width: 100%;
            max-width: 250px;
            justify-content: center;
        }

        .mobile-auth-buttons .btn {
            flex: 1;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .mobile-auth-buttons .btn-outline {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
        }

        .mobile-auth-buttons .btn-outline:hover {
            background: var(--primary-blue);
            color: white;
        }

        .mobile-auth-buttons .btn-primary {
            background: var(--primary-pink);
            color: white;
            border: 2px solid var(--primary-pink);
        }

        .mobile-auth-buttons .btn-primary:hover {
            background: transparent;
            color: var(--primary-pink);
        }

        /* Intro Section Styles */
        .intro-section {
            background-color: #f8f9fa;
            padding: 6rem 2rem;
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            margin-bottom: 3rem;
        }

        .intro-section::before {
            content: '';
            position: absolute;
            top: -150%;
            left: -150%;
            width: 400%;
            height: 400%;
            background-image: url('../static/img/giggenius_logo.jpg');
            background-size: 150px 150px;
            background-repeat: repeat;
            transform: rotate(-15deg);
            opacity: 0.08;
            pointer-events: none;
            z-index: 0;
        }

        .intro-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
            position: relative;
            z-index: 1;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.92) 0%, /* Slightly more transparent */
                rgba(255, 255, 255, 0.82) 100%);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        }

        .intro-title {
            color: var(--primary-blue);
            font-size: 3.5rem;
            margin-bottom: 2.5rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        .intro-title .highlight {
            color: var(--primary-pink);
        }

        .intro-title .highlight::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 0;
            width: 100%;
            height: 8px;
            z-index: -1;
        }

        .intro-text {
            color: var(--text-dark);
            font-size: 1.25rem;
            line-height: 1.8;
            max-width: 900px;
            margin: 0 auto;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }

        .intro-text:last-child {
            margin-bottom: 0;
        }

        /* Add animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .intro-title {
            animation: fadeInUp 0.8s ease-out;
        }

        .intro-text {
            animation: fadeInUp 0.8s ease-out 0.2s;
            animation-fill-mode: backwards;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .intro-section {
                padding: 4rem 1.5rem;
            }

            .intro-title {
                font-size: 2.5rem;
                margin-bottom: 2rem;
            }

            .intro-text {
                font-size: 1.1rem;
                line-height: 1.6;
            }
        }

        @media screen and (max-width: 480px) {
            .intro-title {
                font-size: 2rem;
            }

            .intro-text {
                font-size: 1rem;
            }
        }

        /* Journey Section Styles */
        .journey-section {
            background: var(--primary-blue);
            padding: 4rem 2rem;
            border-radius: 15px;
        }

        .journey-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .journey-title {
            color: var(--text-light);
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .journey-box {
            background: white;
            border: 2px solid var(--primary-pink);
            border-radius: 15px;
            padding: 2rem;
            margin: 0 auto;
            max-width: 1200px;
        }

        .journey-text {
            color: var(--text-dark);
            font-size: 1.25rem;  /* Updated to match intro-text size */
            line-height: 1.8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .journey-text:last-child {
            margin-bottom: 0;
        }

        /* Founder Section Styles */
        .founder-section {
            background: var(--text-light);
            padding: 4rem 2rem;
        }

        .founder-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .founder-content {
            padding-right: 2rem;
        }

        .founder-title {
            color: var(--primary-blue);
            font-size: 2.5rem;
            margin-bottom: 2rem;
            font-weight: 700;
        }

        .founder-text {
            font-size: 1.2rem;
            line-height: 1.8;
            color: var(--text-dark);
            margin-bottom: 2rem;
        }

        .founder-quote {
            font-style: italic;
            color: var(--primary-pink);
            font-size: 1.3rem;
            margin: 2rem 0;
            padding-left: 1rem;
            border-left: 4px solid var(--primary-pink);
        }

        .founder-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .founder-image {
            width: 100%;
            height: auto;
            transition: transform 0.3s ease;
        }

        .founder-image:hover {
            transform: scale(1.05);
        }

        .founder-signature {
            margin-top: 2rem;
            text-align: right;
        }

        .signature-name {
            color: var(--primary-blue);
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .signature-title {
            color: var(--text-dark);
            font-size: 1.1rem;
            font-style: italic;
        }

        /* Mission & Vision Section */
        .mv-section {
            background: var(--primary-pink);
            padding: 4rem 2rem;
            border-radius: 15px;
        }

        .mv-title {
            color: var(--text-light);
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .mv-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .mv-box {
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 15px;
            padding: 2rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .mv-box h3 {
            color: var(--primary-blue);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .mv-box p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--text-dark);
        }

        /* Core Values Section */
        .values-section {
            background: var(--text-light);
            padding: 4rem 2rem;
        }

        .values-title {
            color: var(--primary-blue);
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .values-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .value-box {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            height: 100%;
            border: 2px solid var(--primary-blue);
        }

        .value-box i {
            font-size: 2.5rem;
            color: var(--primary-pink);
            margin-bottom: 1.5rem;
        }

        .value-box h3 {
            color: var(--primary-blue);
            font-size: 1.6rem;
            margin-bottom: 1.5rem;
        }

        .value-box p {
            color: var(--text-dark);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* Team Section */
        .team-section {
            background: #f5f5f5f5;
            padding: 2rem;
            border-radius: 15px;
        }

        .team-title {
            color: var(--primary-blue);
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 2rem;
            padding: 0;
            max-width: 1400px;
            margin: 0 auto;
        }

        .team-member {
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            width: 100%;
        }

        .member-image {
            width: 100%;
            height: 200px;
            margin: 0 auto 1rem;
            border-radius: 10px;
            overflow: hidden;
            max-width: 300px;
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .member-social {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .member-social a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.5rem;
            transition: color 0.3s ease;
        }

        .email-text {
            font-size: 0.9rem;
        }

        .email-icon {
            position: relative;
        }

        .email-icon:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 1000;
        }

        .member-social a:hover {
            color: var(--primary-pink);
        }

        /* Responsive adjustments */
        @media screen and (max-width: 1200px) {
            .team-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .team-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 480px) {
            .team-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Responsive adjustments */
        @media screen and (max-width: 992px) {
            .values-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .values-row {
                grid-template-columns: 1fr;
            }
        }

        /* TV */
        @media screen and (min-width: 1921px) and (max-width: 3840px) {
            .section1 .image {
                display: none;
            }
        }

        @media screen and (max-width: 1500px) {
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        @media screen and (max-width: 1300px) {
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        /* Tablets & Small Laptops */
        @media screen and (max-width: 1200px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        @media screen and (max-width: 1024px) {
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .search-auth-content .search-type-select,
            .search-auth-content .search-bar {
                display: none;
            }
            .mobile-search-icon {
                display: block;
            }
            .auth-buttons {
                display: none;
            }

            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }
        }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        /* Responsive Styles */
        @media screen and (max-width: 992px) {
            .founder-container {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .founder-content {
                padding-right: 0;
                text-align: center;
            }

            .founder-quote {
                text-align: left;
            }

            .founder-signature {
                text-align: center;
            }
            .mv-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .values-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Mobile Devices */
        @media screen and (max-width: 768px) {
            .intro-section, .founder-section {
                padding: 4rem 4%;
            }
            .intro-title, .founder-title {
                font-size: 2.2rem;
            }
            .intro-text, .founder-text {
                font-size: 1.1rem;
            }
            footer {
                padding: 2rem 1rem;
            }
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }
            .footer-column h3 {
                font-size: 1.5rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                gap: 0.5rem;
            }
            .footer-column h3::after {
                content: '▼';
                font-size: 1rem;
                transition: transform 0.3s ease;
                margin-left: auto;
            }
            .footer-column h3.active::after {
                transform: rotate(180deg);
            }
            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .footer-column .footer-links.show {
                max-height: 500px;
            }
            .footer-column a {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }
            .footer-bottom p {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 0.5rem;
            }
            .social-icons .bi {
                font-size: 1.5rem;
            }
            .mv-row, .values-row {
                grid-template-columns: 1fr;
            }

            .mv-section, .values-section {
                padding: 4rem 4%;
            }
        }

        @media screen and (max-width: 600px) {
            .sidebar {
                width: 100% !important;
                margin-bottom: 2rem;
            }
            .main-content {
                padding: 0 1rem !important;
            }
            .section-content {
                padding: 0 1rem;
            }
        }

        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px; /* Add minimum width */
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0;
                background: white;
                z-index: 1003;
            }
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo {
                margin-left: -0.5rem;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 3.5rem;
                height: 3.5rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .mobile-search-icon {
                display: block;
            }
            .navbar .auth-buttons {
                display: none;
            }
            .footer-column h3 {
                font-size: 1.2rem;
            }
            .footer-column a {
                font-size: 1rem;
            }
            .footer-bottom p {
                font-size: 1.1rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                gap: 0.3rem;
                margin-top: 0.5rem;
            }
            .social-icons .bi {
                font-size: 1.5rem;
            }
            .footer-bottom .social-icons img {
                width: 20px;
                height: 20px;
            }
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }
            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                max-height: 90vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.5rem;
                align-items: center;
                text-align: center;
            }
            .modal h2,
            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            .modal .or-separator {
                text-align: center;
            }
            .form-group {
                width: 100%;
                max-width: 100%;
                text-align: center;
                margin-bottom: 15px;
            }
            .form-group input {
                width: 100%;
                text-align: center;
                padding: 12px;
            }
            .role-selection {
                width: 100%;
                text-align: center;
            }
            .role-option {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 15px;
                margin: 10px 0;
            }
            .social-login-btn {
                width: 100%;
                justify-content: center;
                margin: 10px auto;
            }
            .modal-buttons {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .modal-buttons .btn {
                min-width: 140px;
                padding: 0.8rem 1.2rem;
                margin: 0 auto;
            }
            #loginModal,
            #joinModal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }

            .section {
                margin-bottom: 2rem;
            }

            .section-content {
                font-size: 0.9rem;
            }

            ul {
                padding-left: 1.5rem;
            }
        }
        @media (max-width: 360px) {
            /* Header/Navbar adjustments */
            .navbar {
                padding: 0.5rem;
                margin-left: -0.5rem;
            }

            .mobile-search-icon {
                display: block;
            }

            .navbar .auth-buttons {
                display: none;
            }

            .logo {
                margin-left: -0.5rem;
            }
            /* Intro Section */
            .intro-container {
                padding: 1rem;
            }
            .intro-title {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }
            .intro-text {
                font-size: 0.8rem;
                line-height: 1.4;
                margin-bottom: 1rem;
            }

            /* Mission & Vision Section */
            .mv-title, .values-title, .journey-title, .team-title {
                font-size: 1.4rem;
                margin-bottom: 1.5rem;
            }
            .mv-box h3, .value-box h3 {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .mv-box p, .value-box p, .journey-text {
                font-size: 0.8rem;
                line-height: 1.4;
            }

            /* Core Values Section */
            .value-box i {
                font-size: 1.8rem;
                margin-bottom: 0.8rem;
            }

            /* Founder Section */
            .founder-title {
                font-size: 1.4rem;
                margin-bottom: 1rem;
            }
            .founder-text {
                font-size: 0.8rem;
                line-height: 1.4;
            }
            .founder-quote {
                font-size: 0.9rem;
                padding: 0.8rem;
                margin: 1rem 0;
            }
            .signature-name {
                font-size: 1.1rem;
            }
            .signature-title {
                font-size: 0.8rem;
            }

            /* Team Section */
            .team-member h3 {
                font-size: 1.1rem;
                margin: 0.5rem 0 0.2rem;
            }
            .member-role {
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
            }
            .member-social i {
                font-size: 1rem;
            }

            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                padding: 1rem;
                max-height: 85vh;
            }

            .form-group input {
                padding: 0.7rem 1rem 0.7rem 2.2rem;
                font-size: 0.9rem;
            }

            .modal-content h2 {
                font-size: 20px;
            }

            .close {
                right: 0.5rem;
                top: 0;
            }
        }
    </style>
</head>
    <!-- Modal for Login -->
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp2.png') }}" alt="Google">
                    <span>Continue with Google</span>
                </a>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithLinkedIn()">
                    <img src="{{ url_for('static', filename='img/lp5.png') }}" alt="LinkedIn">
                    <span>Continue with LinkedIn</span>
                </a>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Forgot Password -->
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <a href="javascript:void(0)" class="btn btn-primary" onclick="submitForgotPassword()" style="width: auto;">Submit</a>
        </div>
    </div>

    <!-- Modal for Join -->
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <a href="javascript:void(0)" class="btn btn-primary" onclick="continueToRegistration()">Continue</a>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

<body>
    <body>
        <!-- Add this side navigation panel right after the opening <body> tag -->
        <div class="side-nav" id="sideNav">
            <div class="side-nav-content">
                <div class="nav-items">
                    <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                    <a href="{{ url_for('find_geniuses') }}" class="nav-item">Find Geniuses</a>
                    <a href="{{ url_for('find_gigs') }}" class="nav-item">Find Gigs</a>
                    <a href="{{ url_for('about_us') }}" class="nav-item active">About Us</a>
                    <div class="mobile-auth-buttons">
                        <a href="javascript:void(0)" onclick="openLoginModal(); toggleMenu();" class="btn btn-outline">Log In</a>
                        <a href="javascript:void(0)" onclick="openModal(); toggleMenu();" class="btn btn-primary">Join</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

        <!-- Mobile Search Overlay -->
        <div class="search-overlay" id="searchOverlay">
            <div class="expanded-search-container">
                <div class="expanded-search-header">
                    <h3>Search</h3>
                    <button class="close-search-btn" onclick="closeMobileSearch()">&times;</button>
                </div>
                <div class="expanded-search-form">
                    <div class="expanded-search-type">
                        <div class="search-type-chip active" data-type="all">All</div>
                        <div class="search-type-chip" data-type="genius">Geniuses</div>
                        <div class="search-type-chip" data-type="gigs">Gigs</div>
                    </div>
                    <div class="expanded-search-input">
                        <input type="text" id="mobileSearchInput" placeholder="Search...">
                        <button class="search-btn" onclick="performMobileSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container">
            <!-- Navbar -->
            <nav class="navbar">
                <div style="display: flex; align-items: center;">
                    <button class="hamburger" onclick="toggleMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                    </a>
                    <div class="nav-links">
                        <a href="{{ url_for('landing_page') }}">Home</a>
                        <a href="{{ url_for('find_geniuses') }}">Find Geniuses</a>
                        <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                        <a href="{{ url_for('about_us') }}" class="active">About Us</a>
                    </div>
                </div>
                <div class="navbar-right">
                    <div class="search-auth-content">
                        <div class="search-type-select">
                            <button class="search-type-button" id="searchTypeBtn">
                                <span id="selectedSearchType">All</span>
                            </button>
                            <div class="search-type-dropdown" id="searchTypeDropdown">
                                <div class="search-type-option" data-value="all">All</div>
                                <div class="search-type-option" data-value="genius">Geniuses</div>
                                <div class="search-type-option" data-value="gigs">Gigs</div>
                            </div>
                        </div>
                        <div class="search-bar">
                            <input type="text" id="searchInput" placeholder="Search...">
                            <i class="fas fa-search icon"></i>
                        </div>
                        <button class="mobile-search-icon" onclick="openMobileSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="auth-buttons">
                            <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                            <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
                        </div>
                    </div>
                </div>
            </nav>

            <main class="main-content">
                <!-- Intro Section -->
            <section class="intro-section">
                <div class="intro-container">
                    <h1 class="intro-title">Welcome to <span class="highlight">GigGenius</span></h1>
                    <p class="intro-text">
                        GigGenius was built with the purest intention: to provide a secure platform, free from fake job postings. We take time to manually check all the profiles of the users signing up to our system before they can complete use it to avoid scammers.
                    </p>
                    <p class="intro-text">
                        We created GigGenius because we saw a broken system, freelancers struggling to find work, burdened by fees and unfair competition. As a freelancer, I knew there had to be a better way. It was built for freelancers and business owners to use freely, without the need to purchase connects or bid credits simply to apply for a job. It offers equal opportunities to all freelancers, ensuring a level playing field without systems that artificially boost profiles of those who have more budget to spend.
                    </p>
                </div>
            </section>

            <!-- Mission & Vision Section -->
            <section class="mv-section">
                <h2 class="mv-title">MISSION & VISION</h2>
                <div class="mv-row">
                    <div class="mv-box">
                        <h3>Mission</h3>
                        <p>To empower freelancers and businesses by providing a secure, transparent, and equitable platform for connecting talent with opportunities, free from the burdens of unfair fees and manipulative systems.</p>
                    </div>

                    <div class="mv-box">
                        <h3>Vision</h3>
                        <p>A future where every freelancer has equal access to fulfilling work, and every business can easily find the skilled talent they need, all within a trustworthy and supportive online environment.</p>
                    </div>

                    <div class="mv-box">
                        <h3>Guarantee</h3>
                        <p>GigGenius guarantees a safe and fair platform where all profiles are manually vetted to prevent scams and ensure a level playing field for all freelancers. We are committed to providing a free and accessible platform, free from hidden fees or artificial ranking systems that favor those with larger budgets. We believe in empowering talent, not exploiting it.</p>
                    </div>
                </div>
            </section>

            <!-- Core Values Section -->
            <section class="values-section">
                <h2 class="values-title">OUR CORE VALUES</h2>
                <div class="values-row">
                    <div class="value-box">
                        <i class="bi bi-brightness-high"></i>
                        <h3>Transparency</h3>
                        <p>We believe in clear communication, honest pricing, and visible processes. No hidden fees, no surprises - just straightforward business practices.</p>
                    </div>

                    <div class="value-box">
                        <i class="bi bi-people-fill"></i>
                        <h3>Community First</h3>
                        <p>We foster genuine connections and collaborative growth. Our platform is built on the principle that success is better when shared and celebrated together.</p>
                    </div>

                    <div class="value-box">
                        <i class="bi bi-universal-access"></i>
                        <h3>Accessibility</h3>
                        <p>We create tools and opportunities that are available to everyone, regardless of background or circumstance. Equal opportunities for all.</p>
                    </div>

                    <div class="value-box">
                        <i class="bi bi-lightbulb"></i>
                        <h3>Innovation</h3>
                        <p>We constantly evolve our platform based on community feedback and emerging needs, staying ahead of industry trends to serve you better.</p>
                    </div>

                    <div class="value-box">
                        <i class="bi bi-shield-check"></i>
                        <h3>Trust & Security</h3>
                        <p>We prioritize the safety of our community through robust verification processes, secure payments, and protected communications.</p>
                    </div>

                    <div class="value-box">
                        <i class="bi bi-globe"></i>
                        <h3>Global Inclusion</h3>
                        <p>We embrace diversity and create opportunities for talent worldwide, breaking down geographical barriers in the digital workspace.</p>
                    </div>
                </div>
            </section>

            <!-- Journey Section -->
            <section class="journey-section">
                <div class="journey-container">
                    <h2 class="journey-title">OUR JOURNEY</h2>
                    <div class="journey-box">
                        <p class="journey-text">
                            Launching GigGenius took considerable time and effort. The platform was rebuilt 3x times to ensure we achieved the right approach. But our vision remained clear: to create a platform that truly serves its community.
                        </p>
                        <p class="journey-text">
                            GigGenius is more than just a job platform; it's a vibrant community where freelancers and businesses thrive. We plan to have networking events where we provide workshops for freelancers and have business networking events for business owners for them to collaborate more giving more opportunities to everyone.
                        </p>
                        <p class="journey-text">
                            GigGenius believes in creating economic opportunities for everyone. Our vision goes beyond empowering freelancers and business owners.  That's why we're committed to reaching out to underserved communities, especially elderly and underprivileged children.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Founder Section -->
            <section class="founder-section">
                <div class="founder-container">
                    <div class="founder-content">
                        <h2 class="founder-title">Meet Our Founder</h2>
                        <p class="founder-text">
                            With the collaborative effort of our talented team, GigGenius came to life. While it took time to launch due to balancing multiple client projects, our shared vision remained clear: to create a platform that truly serves its community.
                        </p>
                        <div class="founder-quote">
                            "GigGenius is a force for good, changing lives and building a brighter future. Join us and be part of something truly extraordinary."
                        </div>
                        <p class="founder-text">
                            Our vision goes beyond empowering freelancers. We believe in creating economic opportunities for everyone. That's why we're committed to reaching out to underserved communities, including the elderly and underprivileged children.
                        </p>
                        <div class="founder-signature">
                            <div class="signature-name">HAINZ'EL LLANELY CANTOS</div>
                            <div class="signature-title">Founder and CEO</div>
                        </div>
                    </div>
                    <div class="founder-image-container">
                        <img src="{{ url_for('static', filename='img/au1.jpg') }}" alt="Founder of GigGenius" class="founder-image">
                    </div>
                </div>
            </section>

            <!-- Our Team Section -->
            <section class="team-section">
                <h2 class="team-title">Meet the Geniuses Behind <span style="color: var(--primary-pink);">GigGenius</span></h2>
                <div class="team-grid">
                    <div class="team-member">
                        <div class="member-image">
                            <img src="{{ url_for('static', filename='img/au2_lesly.jpg') }}" alt="Image of Lesly-Ann B. Victoria">
                        </div>
                        <h3>Lesly-Ann B. Victoria</h3>
                        <p class="member-role">Full-stack Developer</p>
                        <div class="member-social">
                            <a href="mailto:<EMAIL>" target="_blank" class="email-icon" data-tooltip="<EMAIL>"><i class="bi bi-envelope-fill"></i></a>
                            <a href="https://www.facebook.com/leslyann.victoria/" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="https://www.linkedin.com/in/leslyann-victoria" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="https://github.com/LeslyVictoria" target="_blank"><i class="bi bi-github"></i></a>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="{{ url_for('static', filename='img/au3_carlo.jpeg') }}" alt="Image of Carlo R. Caburnay">
                        </div>
                        <h3>Carlo R. Caburnay</h3>
                        <p class="member-role">Full-stack Developer</p>
                        <div class="member-social">
                            <a href="#" target="_blank"><i class="bi bi-envelope-fill"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-github"></i></a>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="{{ url_for('static', filename='img/au4_richard.png') }}" alt="Image of John Richard L. Bercades">
                        </div>
                        <h3>John Richard L. Bercades</h3>
                        <p class="member-role">Full-stack Developer</p>
                        <div class="member-social">
                            <a href="mailto:<EMAIL>" target="_blank" class="email-icon" data-tooltip="<EMAIL>"><i class="bi bi-envelope-fill"></i></a>
                            <a href="https://www.facebook.com/JohnRichardBercades/" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="https://www.linkedin.com/in/bercades-john-richard-l-28b2422aa/" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="https://github.com/John-Richard-Bercades" target="_blank"><i class="bi bi-github"></i></a>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="{{ url_for('static', filename='img/au5_nash.png') }}" alt="Image of Nashrudin Maverick A. Esguerra">
                        </div>
                        <h3>Nashrudin Maverick A. Esguerra</h3>
                        <p class="member-role">Full-stack Developer</p>
                        <div class="member-social">
                            <a href="#" target="_blank"><i class="bi bi-envelope-fill"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-github"></i></a>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="{{ url_for('static', filename='img/au6_jandel.jpg') }}" alt="Image of Niño Jandel C. Magpantay">
                        </div>
                        <h3>Niño Jandel C. Magpantay</h3>
                        <p class="member-role">Full-stack Developer</p>
                        <div class="member-social">
                            <a href="#" target="_blank"><i class="bi bi-envelope-fill"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-facebook"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-linkedin"></i></a>
                            <a href="#" target="_blank"><i class="bi bi-github"></i></a>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Webinars</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                    <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                        <a href="https://www.instagram.com/gig.genius.io/" class="bi bi-instagram"></a>
                        <a href="https://www.threads.com/@gig.genius.io" class="bi bi-threads"></a>
                        <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                        <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                        <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                        <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                    </span>
                </p>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-33SRJGWX6H"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-33SRJGWX6H');
    </script>

    <script>
        // Navbar
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');

            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Animate hamburger
            if (sideNav.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
                document.body.style.overflow = 'auto'; // Restore scrolling when menu is closed
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');

            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        // Mobile Search Functions
        function openMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;

            searchOverlay.classList.add('active');
            body.style.overflow = 'hidden';
            body.style.position = 'fixed';
            body.style.width = '100%';

            // Focus on search input after animation
            setTimeout(() => {
                document.getElementById('mobileSearchInput').focus();
            }, 300);
        }

        function closeMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;

            searchOverlay.classList.remove('active');
            body.style.overflow = 'auto';
            body.style.position = 'static';
            body.style.width = 'auto';
        }

        function performMobileSearch() {
            const searchInput = document.getElementById('mobileSearchInput');
            const searchTerm = searchInput.value.trim();
            const activeChip = document.querySelector('.search-type-chip.active');
            const searchType = activeChip ? activeChip.dataset.type : 'all';

            if (searchTerm) {
                // Perform search logic here
                console.log('Searching for:', searchTerm, 'Type:', searchType);
                // You can add your search logic here

                // Close the search overlay
                closeMobileSearch();

                // Clear the input
                searchInput.value = '';
            }
        }

        // Handle search type chip selection
        document.addEventListener('DOMContentLoaded', function() {
            const searchChips = document.querySelectorAll('.search-type-chip');
            const mobileSearchInput = document.getElementById('mobileSearchInput');

            searchChips.forEach(chip => {
                chip.addEventListener('click', function() {
                    // Remove active class from all chips
                    searchChips.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked chip
                    this.classList.add('active');

                    // Update placeholder based on selection
                    const placeholders = {
                        genius: 'Search for geniuses...',
                        gigs: 'Search for gigs...',
                        all: 'Search...'
                    };
                    const type = this.dataset.type;
                    mobileSearchInput.placeholder = placeholders[type] || placeholders.all;
                });
            });

            // Handle Enter key in mobile search
            mobileSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performMobileSearch();
                }
            });

            // Close search overlay when clicking outside
            document.getElementById('searchOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeMobileSearch();
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');

            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Toggle active class on heading
                    this.classList.toggle('active');

                    // Get the next sibling element (the links container)
                    const linksContainer = this.nextElementSibling;

                    // Toggle the show class
                    linksContainer.classList.toggle('show');
                });
            });
        });

        let selectedRole = null;

        // Modal
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals();
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        // Join Modal
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }

        function continueToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');

            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }

            if (geniusRole) {
                window.location.href = "{{ url_for('genius_registration') }}";
            } else {
                window.location.href = "{{ url_for('client_registration') }}";
            }
        }

        // Login Modal
        function openLoginModal() {
            closeAllModals();
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals();
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            closeForgotPasswordModal();
        }

        // Security Code Modal
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        // Scroll to Section
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Search Type Dropdown
        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });
        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Login response:', data);  // Debug log

                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    const errorMessage = document.getElementById('loginErrorMessage');
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Login error:', error);  // Debug log
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = 'An error occurred during login';
                errorMessage.style.display = 'block';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Get all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

            // Add click event listener to each link
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    sidebarLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Scroll to the target section
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
    </script>
</body>
</html>